package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.arealytics.areadocs.domain.Address;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.domain.Folder;
import com.arealytics.areadocs.domain.States;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.domain.ZipCodes;
import com.arealytics.areadocs.dto.requestDTO.BulkUserOperationRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.BulkUserOperationResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;
import com.arealytics.areadocs.enumeration.AddressType;
import com.arealytics.areadocs.enumeration.DeactivationReason;
import com.arealytics.areadocs.enumeration.FolderType;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.AddressMapper;
import com.arealytics.areadocs.mapper.CompanyMapper;
import com.arealytics.areadocs.mapper.CompanyRequestMapper;
import com.arealytics.areadocs.repository.*;

import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyService {

    private final CompanyRepository companyRepository;
    private final CompanyMapper companyMapper;
    private final AccountService accountService;
    private final AddressMapper addressMapper;
    private final CompanyRequestMapper companyRequestMapper;
    private final UserRepository userRepository;
    private final FolderService folderService;
    private final DocumentRepository documentRepository;
    private final OrderItemRepository orderItemRepository;
    private final StatesRepository statesRepository;
    private final ZipCodesRepository zipCodesRepository;
    private final BulkUserOperationService bulkUserOperationService;
    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final DocumentPriceRepository documentPriceRepository;

    /**
     * Create a new company
     *
     * @param companyRequestDTO The company to create
     * @return The created company DTO
     */
    @Transactional
    public CompanyDTO createCompany(CompanyRequestDTO companyRequestDTO) {
        if (companyRepository.existsByName(companyRequestDTO.getName())) {
            throw new CompanyException(
                    "Company with name '" + companyRequestDTO.getName() + "' already exists");
        }

        Company company = companyRequestMapper.toEntity(companyRequestDTO);

        // Handle addresses
        if (companyRequestDTO.getPrimaryAddress() != null) {
            Address primaryAddress = addressMapper.toEntity(companyRequestDTO.getPrimaryAddress());
            primaryAddress.setAddressType(AddressType.PRIMARY);
            States state =
                    statesRepository
                            .findById(companyRequestDTO.getPrimaryAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + companyRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(companyRequestDTO.getPrimaryAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + companyRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getZipCodeId()));
            primaryAddress.setState(state);
            primaryAddress.setZipCode(zipCode);
            company.setPrimaryAddress(primaryAddress);

            if (Boolean.TRUE.equals(companyRequestDTO.getIsBillingPrimary())) {
                company.setBillingAddress(primaryAddress);
            } else if (companyRequestDTO.getBillingAddress() != null) {
                Address billingAddress =
                        addressMapper.toEntity(companyRequestDTO.getBillingAddress());
                billingAddress.setAddressType(AddressType.BILLING);
                state =
                        statesRepository
                                .findById(companyRequestDTO.getBillingAddress().getStateId())
                                .orElseThrow(
                                        () ->
                                                new EntityNotFoundException(
                                                        "State not found with id: "
                                                                + companyRequestDTO
                                                                        .getBillingAddress()
                                                                        .getStateId()));
                zipCode =
                        zipCodesRepository
                                .findById(companyRequestDTO.getBillingAddress().getZipCodeId())
                                .orElseThrow(
                                        () ->
                                                new EntityNotFoundException(
                                                        "ZipCode not found with id: "
                                                                + companyRequestDTO
                                                                        .getBillingAddress()
                                                                        .getZipCodeId()));
                billingAddress.setState(state);
                billingAddress.setZipCode(zipCode);
                company.setBillingAddress(billingAddress);
            } else {
                throw new CompanyException(
                        "Billing address is required when isBillingPrimary is false");
            }
        } else {
            throw new CompanyException("Primary address is required");
        }

        Company savedCompany = companyRepository.save(company);

        // Create an account for the company
        log.info("Creating account for new company with ID: {}", savedCompany.getId());
        accountService.createCompanyAccount(savedCompany);

        // Create the root folder for the company
        log.info("Creating root folder for new company with ID: {}", savedCompany.getId());
        Folder rootFolder =
                folderService.createSystemFolder(null, savedCompany, FolderType.ROOT, null);

        List<DocumentPrice> documentPrices = documentPriceRepository.findAll();
        List<CompanySpecialPrice> companySpecialPrices = new ArrayList<>();
        for (DocumentPrice documentPrice : documentPrices) {
            if (!companySpecialPriceRepository.existsByCompanyIdAndDocumentPriceId(
                    savedCompany.getId(), documentPrice.getId())) {
                CompanySpecialPrice companySpecialPrice =
                        CompanySpecialPrice.builder()
                                .documentPrice(documentPrice)
                                .company(savedCompany)
                                .specialPrice(documentPrice.getBasePrice())
                                .specialPriceGst(documentPrice.getEffectivePriceGst())
                                .build();
                companySpecialPrices.add(companySpecialPrice);
            }
        }
        if (!companySpecialPrices.isEmpty()) {
            companySpecialPriceRepository.saveAll(companySpecialPrices);
        }

        return companyMapper.toDto(savedCompany);
    }

    @Transactional
    public CompanyDTO updateCompany(Long id, CompanyRequestDTO companyRequestDTO) {
        Company existingCompany =
                companyRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with id: " + id));

        // Check for duplicate name
        if (!existingCompany.getName().equals(companyRequestDTO.getName())
                && companyRepository.existsByName(companyRequestDTO.getName())) {
            throw new CompanyException(
                    "Company with name '" + companyRequestDTO.getName() + "' already exists");
        }

        // Check for duplicate billing email
        if (!existingCompany.getBillingEmail().equals(companyRequestDTO.getBillingEmail())
                && companyRepository.existsByBillingEmail(companyRequestDTO.getBillingEmail())) {
            throw new CompanyException(
                    "Company with billing email '"
                            + companyRequestDTO.getBillingEmail()
                            + "' already exists");
        }

        // Update company fields from DTO
        companyRequestMapper.updateCompanyFromDto(companyRequestDTO, existingCompany);

        // PRIMARY address update logic
        if (companyRequestDTO.getPrimaryAddress() != null) {
            Address primaryAddress = existingCompany.getPrimaryAddress();
            if (primaryAddress == null) {
                primaryAddress = new Address();
                primaryAddress.setAddressType(AddressType.PRIMARY);
            }
            addressMapper.updateAddressFromDTO(
                    companyRequestDTO.getPrimaryAddress(), primaryAddress);
            States state =
                    statesRepository
                            .findById(companyRequestDTO.getPrimaryAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + companyRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(companyRequestDTO.getPrimaryAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + companyRequestDTO
                                                                    .getPrimaryAddress()
                                                                    .getZipCodeId()));
            primaryAddress.setState(state);
            primaryAddress.setZipCode(zipCode);
            existingCompany.setPrimaryAddress(primaryAddress);
        }

        // BILLING address logic
        if (Boolean.TRUE.equals(companyRequestDTO.getIsBillingPrimary())) {
            // When using primary as billing, ensure we have a primary address first
            if (existingCompany.getPrimaryAddress() == null) {
                throw new CompanyException(
                        "Cannot use primary address as billing address when primary address is not"
                                + " set");
            }
            existingCompany.setBillingAddress(existingCompany.getPrimaryAddress());
        } else if (companyRequestDTO.getBillingAddress() != null) {
            Address billingAddress = existingCompany.getBillingAddress();
            if (billingAddress == null
                    || billingAddress.equals(existingCompany.getPrimaryAddress())) {
                billingAddress = new Address();
                billingAddress.setAddressType(AddressType.BILLING);
            }
            addressMapper.updateAddressFromDTO(
                    companyRequestDTO.getBillingAddress(), billingAddress);
            States state =
                    statesRepository
                            .findById(companyRequestDTO.getBillingAddress().getStateId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "State not found with id: "
                                                            + companyRequestDTO
                                                                    .getBillingAddress()
                                                                    .getStateId()));
            ZipCodes zipCode =
                    zipCodesRepository
                            .findById(companyRequestDTO.getBillingAddress().getZipCodeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "ZipCode not found with id: "
                                                            + companyRequestDTO
                                                                    .getBillingAddress()
                                                                    .getZipCodeId()));
            billingAddress.setState(state);
            billingAddress.setZipCode(zipCode);
            existingCompany.setBillingAddress(billingAddress);
        } else {
            throw new CompanyException(
                    "Billing address is required when isBillingPrimary is false");
        }

        try {
            Company savedCompany = companyRepository.save(existingCompany);
            return companyMapper.toDto(savedCompany);
        } catch (Exception e) {
            log.error("Error updating company: ", e);
            throw new CompanyException("Failed to update company: " + e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public CompanyDTO getCompanyById(Long id) {
        Company company =
                companyRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with id: " + id));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by name
     *
     * @param name The company name
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByName(String name) {
        Company company =
                companyRepository
                        .findByName(name)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with name: " + name));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by abn
     *
     * @param abn The company abn
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByABN(String abn) {
        Company company =
                companyRepository
                        .findByABN(abn)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with abn: " + abn));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by ACN
     *
     * @param acn The company acn
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByACN(String acn) {
        Company company =
                companyRepository
                        .findByACN(acn)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with acn: " + acn));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by billing email
     *
     * @param accountsContactNumber The billing email
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByAccountsContactNumber(String accountsContactNumber) {
        Company company =
                companyRepository
                        .findByAccountsContactNumber(accountsContactNumber)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with Accounts ContactNumber: "
                                                        + accountsContactNumber));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by billing email
     *
     * @param billingEmail The billing email
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByBillingEmail(String billingEmail) {
        Company company =
                companyRepository
                        .findByBillingEmail(billingEmail)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with Billing Email: "
                                                        + billingEmail));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get a company by accounts contact name
     *
     * @param accountsContactName The accounts contact name
     * @return The company DTO
     */
    @Transactional(readOnly = true)
    public CompanyDTO getCompanyByAccountsContactName(String accountsContactName) {
        Company company =
                companyRepository
                        .findByAccountsContactName(accountsContactName)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with account contact name: "
                                                        + accountsContactName));
        CompanyDTO companyDTO = companyMapper.toDto(company);
        companyDTO.setTotalDocumentsOrdered(documentRepository.countByCompanyId(company.getId()));
        companyDTO.setTotalDocumentPrice(
                orderItemRepository.sumFinalPriceByDocumentCompanyId(company.getId()));
        return companyDTO;
    }

    /**
     * Get companies by industry
     *
     * @param industry The industry
     * @return List of company DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyDTO> getCompaniesByIndustry(String industry) {
        return companyRepository.findByIndustry(industry).stream()
                .map(companyMapper::toDto)
                .peek(
                        companyDTO -> {
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(companyDTO.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            companyDTO.getId()));
                        })
                .collect(Collectors.toList());
    }

    /**
     * Get companies by employee count greater than
     *
     * @param employeeCount The employee count
     * @return List of company DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyDTO> getCompaniesByEmployeeCountGreaterThan(Integer employeeCount) {
        return companyRepository.findByEmployeeCountGreaterThan(employeeCount).stream()
                .map(companyMapper::toDto)
                .peek(
                        companyDTO -> {
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(companyDTO.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            companyDTO.getId()));
                        })
                .collect(Collectors.toList());
    }

    /**
     * Get companies by employee count less than
     *
     * @param employeeCount The employee count
     * @return List of company DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyDTO> getCompaniesByEmployeeCountLessThan(Integer employeeCount) {
        return companyRepository.findByEmployeeCountLessThan(employeeCount).stream()
                .map(companyMapper::toDto)
                .peek(
                        companyDTO -> {
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(companyDTO.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            companyDTO.getId()));
                        })
                .collect(Collectors.toList());
    }

    /**
     * Get all companies
     *
     * @return List of all company DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyDTO> getAllCompanies() {
        return companyRepository.findAll().stream()
                .map(companyMapper::toDto)
                .peek(
                        companyDTO -> {
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(companyDTO.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            companyDTO.getId()));
                        })
                .collect(Collectors.toList());
    }

    /**
     * Delete a company
     *
     * @param id The company ID
     */
    @Transactional
    public void deleteCompany(Long id) {
        if (!companyRepository.existsById(id)) {
            throw new EntityNotFoundException("Company not found with id: " + id);
        }

        companyRepository.deleteById(id);
    }

    /**
     * Get companies with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of company DTOs
     */
    @Transactional(readOnly = true)
    public Page<CompanyDTO> getCompaniesWithFilters(CompanyFilterDTO filter, Pageable pageable) {
        Specification<Company> spec =
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (filter != null) {
                        if (StringUtils.hasText(filter.getName())) {
                            predicates.add(
                                    criteriaBuilder.like(
                                            criteriaBuilder.lower(root.get("name")),
                                            "%" + filter.getName().toLowerCase() + "%"));
                        }

                        if (StringUtils.hasText(filter.getABN())) {
                            predicates.add(criteriaBuilder.equal(root.get("abn"), filter.getABN()));
                        }

                        if (StringUtils.hasText(filter.getACN())) {
                            predicates.add(criteriaBuilder.equal(root.get("acn"), filter.getACN()));
                        }

                        if (StringUtils.hasText(filter.getAccountsContactName())) {
                            predicates.add(
                                    criteriaBuilder.like(
                                            criteriaBuilder.lower(root.get("accountsContactName")),
                                            "%"
                                                    + filter.getAccountsContactName().toLowerCase()
                                                    + "%"));
                        }
                        if (StringUtils.hasText(filter.getAccountsContactNumber())) {
                            predicates.add(
                                    criteriaBuilder.like(
                                            criteriaBuilder.lower(
                                                    root.get("accountsContactNumber")),
                                            "%"
                                                    + filter.getAccountsContactNumber()
                                                            .toLowerCase()
                                                    + "%"));
                        }
                        if (StringUtils.hasText(filter.getBillingEmail())) {
                            predicates.add(
                                    criteriaBuilder.like(
                                            criteriaBuilder.lower(root.get("billingEmail")),
                                            "%" + filter.getBillingEmail().toLowerCase() + "%"));
                        }
                        if (StringUtils.hasText(filter.getIndustry())) {
                            predicates.add(
                                    criteriaBuilder.equal(
                                            criteriaBuilder.lower(root.get("industry")),
                                            filter.getIndustry().toLowerCase()));
                        }

                        if (filter.getMinEmployeeCount() != null) {
                            predicates.add(
                                    criteriaBuilder.greaterThanOrEqualTo(
                                            root.get("employeeCount"),
                                            filter.getMinEmployeeCount()));
                        }

                        if (filter.getMaxEmployeeCount() != null) {
                            predicates.add(
                                    criteriaBuilder.lessThanOrEqualTo(
                                            root.get("employeeCount"),
                                            filter.getMaxEmployeeCount()));
                        }
                    }

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };
        companyRepository.findAll(spec, pageable).map(companyMapper::toDto);

        // set the total number of active users in the company
        Page<CompanyDTO> companyDTOPage =
                companyRepository.findAll(spec, pageable).map(companyMapper::toDto);
        companyDTOPage
                .getContent()
                .forEach(
                        companyDTO -> {
                            companyDTO.setActiveUserCount(
                                    userRepository
                                            .countByIsActiveTrueAndCompanyMemberships_IsActiveTrueAndCompanyMemberships_Company_Id(
                                                    companyDTO.getId()));
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(companyDTO.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            companyDTO.getId()));
                        });

        return companyDTOPage;
    }

    /**
     * Get company by ID as Optional
     *
     * @param id The company ID
     * @return Optional of company DTO
     */
    @Transactional(readOnly = true)
    public Optional<CompanyDTO> findCompanyById(Long id) {
        return companyRepository
                .findById(id)
                .map(
                        company -> {
                            CompanyDTO companyDTO = companyMapper.toDto(company);
                            companyDTO.setTotalDocumentsOrdered(
                                    documentRepository.countByCompanyId(company.getId()));
                            companyDTO.setTotalDocumentPrice(
                                    orderItemRepository.sumFinalPriceByDocumentCompanyId(
                                            company.getId()));
                            return companyDTO;
                        });
    }

    @Transactional(rollbackFor = Exception.class)
    public CompanyDTO updateCompanyActiveStatus(Long companyId, Boolean isActive) {
        if (isActive == null) {
            throw new CompanyException("Active status cannot be null.");
        }

        Company company =
                companyRepository
                        .findById(companyId)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with ID: " + companyId));

        List<User> companyUsers = userRepository.findByCompanyMemberships_Company_Id(companyId);

        if (companyUsers.isEmpty()) {
            company.setIsActive(isActive);
            Company updatedCompany = companyRepository.save(company);
            return companyMapper.toDto(updatedCompany);
        }

        List<User> usersToUpdate = new ArrayList<>();
        List<String> usernamesToBulkUpdate = new ArrayList<>();

        // Prepare users to update based on company status
        for (User user : companyUsers) {
            if (!isActive && user.getIsActive()) {
                log.debug(
                        "Preparing to deactivate user {} for company ID: {}",
                        user.getEmail(),
                        companyId);
                user.setIsActive(false);
                user.setDeactivationReason(DeactivationReason.COMPANY_DEACTIVATED);
                user.setDeleteAt(Instant.now());
                usersToUpdate.add(user);
                usernamesToBulkUpdate.add(user.getEmail());
            } else if (isActive && !user.getIsActive()) {
                log.debug(
                        "Preparing to reactivate user {} for company ID: {}",
                        user.getEmail(),
                        companyId);
                user.setIsActive(true);
                user.setDeactivationReason(null);
                user.setDeleteAt(null);
                usersToUpdate.add(user);
                usernamesToBulkUpdate.add(user.getEmail());
            } else {
                log.debug(
                        "Skipping user {}: already in desired state (isActive={})",
                        user.getEmail(),
                        user.getIsActive());
            }
        }

        // Step 1: Update company status in database first
        company.setIsActive(isActive);
        Company updatedCompany = companyRepository.save(company);

        // Step 2: Update users in local database
        if (!usersToUpdate.isEmpty()) {
            userRepository.saveAll(usersToUpdate);
        }

        // Step 3: Perform bulk Keycloak operation
        CompanyDTO result = companyMapper.toDto(updatedCompany);

        if (!usernamesToBulkUpdate.isEmpty()) {
            performKeycloakBulkOperationAsync(companyId, usernamesToBulkUpdate, isActive);
        }

        return result;
    }

    /**
     * Perform Keycloak bulk operation asynchronously to avoid transaction rollback This method runs
     * outside the main transaction to ensure database changes persist even if Keycloak operation
     * fails.
     */
    private void performKeycloakBulkOperationAsync(
            Long companyId, List<String> usernames, Boolean isActive) {
        try {
            BulkUserOperationRequestDTO bulkRequest = new BulkUserOperationRequestDTO();
            bulkRequest.setUsernames(usernames);
            bulkRequest.setOperation(isActive ? "enable" : "disable");

            BulkUserOperationResponseDTO bulkResponse =
                    bulkUserOperationService.performBulkOperation(bulkRequest);

            if (bulkResponse.getFailureCount() > 0) {
                log.warn(
                        "Some users failed to update in Keycloak for company ID: {}. Results: {}",
                        companyId,
                        bulkResponse.getResults());
            }

        } catch (Exception e) {
            log.error(
                    "Error performing bulk Keycloak operation for company ID {}: {}",
                    companyId,
                    e.getMessage(),
                    e);
            log.warn(
                    "Database updates completed but Keycloak sync failed for company ID: {}. "
                            + "Manual reconciliation may be required.",
                    companyId);
        }
    }
}
